******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 14:27:39 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002b09


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004d70  0001b290  R  X
  SRAM                  20200000   00008000  0000092b  000076d5  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004d70   00004d70    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003208   00003208    r-x .text
  000032c8    000032c8    00001a50   00001a50    r-- .rodata
  00004d18    00004d18    00000058   00000058    r-- .cinit
20200000    20200000    0000072e   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    000001ce   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003208     
                  000000c0    000005d2     Ganway.o (.text.Way)
                  00000692    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000694    000001d0     oled.o (.text.OLED_ShowChar)
                  00000864    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b8a    00000002     --HOLE-- [fill = 0]
                  00000b8c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000d14    00000124     empty.o (.text.main)
                  00000e38    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f58    0000010c     motor.o (.text.Set_PWM)
                  00001064    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001170    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001274    000000f4     empty.o (.text.TIMG0_IRQHandler)
                  00001368    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001450    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001534    000000e2     oled.o (.text.OLED_ShowNum)
                  00001616    000000de     oled.o (.text.OLED_Init)
                  000016f4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000017d0    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000018a0    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000194a    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  000019e4    0000009a     oled.o (.text.OLED_ShowString)
                  00001a7e    00000002     --HOLE-- [fill = 0]
                  00001a80    00000090     oled.o (.text.OLED_DrawPoint)
                  00001b10    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b9c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001c28    00000084     oled.o (.text.OLED_Refresh)
                  00001cac    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001d30    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001dac    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001e20    00000074     key.o (.text.key_debounce_process)
                  00001e94    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001f06    00000002     --HOLE-- [fill = 0]
                  00001f08    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001f74    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001fe0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002048    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000020aa    00000002     --HOLE-- [fill = 0]
                  000020ac    00000060     oled.o (.text.OLED_Clear)
                  0000210c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000216a    00000002     --HOLE-- [fill = 0]
                  0000216c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000021c4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002218    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002268    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000022b8    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002304    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002350    0000004c     key.o (.text.Key)
                  0000239c    0000004c     key.o (.text.Key_1)
                  000023e8    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002432    00000002     --HOLE-- [fill = 0]
                  00002434    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000247e    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  000024c8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002510    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002558    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  000025a0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000025e8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000262c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000266e    00000002     --HOLE-- [fill = 0]
                  00002670    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000026b0    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  000026f0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002730    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000276c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000027a8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000027e4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002820    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000285a    00000002     --HOLE-- [fill = 0]
                  0000285c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002890    00000034     oled.o (.text.OLED_ColorTurn)
                  000028c4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000028f8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000292c    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  0000295c    00000030     oled.o (.text.OLED_Pow)
                  0000298c    00000030     systick.o (.text.SysTick_Handler)
                  000029bc    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000029e8    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002a14    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002a40    00000028     empty.o (.text.DL_Common_updateReg)
                  00002a68    00000028     oled.o (.text.DL_Common_updateReg)
                  00002a90    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002ab8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002ae0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002b08    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002b30    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002b56    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002b7c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002ba0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002bc0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002be0    00000020     systick.o (.text.delay_ms)
                  00002c00    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002c1e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002c3c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002c58    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002c74    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002c90    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002cac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002cc8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002ce4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002d00    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002d1c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002d38    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002d54    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002d70    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002d8c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002da8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002dc0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002dd8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002df0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002e08    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002e20    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002e38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002e50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002e68    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002e80    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002e98    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002eb0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002ec8    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00002ee0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002ef8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002f10    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002f28    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002f40    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002f58    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002f70    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002f88    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002fa0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002fb8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002fd0    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002fe8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003000    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003018    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  0000302e    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003044    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000305a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003070    00000016     key.o (.text.DL_GPIO_readPins)
                  00003086    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000309c    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000030b0    00000014     empty.o (.text.DL_GPIO_clearPins)
                  000030c4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000030d8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000030ec    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003100    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003114    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003128    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000313c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003150    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003164    00000014     motor.o (.text.Left_Control)
                  00003178    00000014     motor.o (.text.Left_Little_Control)
                  0000318c    00000014     motor.o (.text.Right_Control)
                  000031a0    00000014     motor.o (.text.Right_Little_Control)
                  000031b4    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000031c6    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000031d8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000031ea    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000031fc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000320e    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  0000321e    00000002     --HOLE-- [fill = 0]
                  00003220    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003230    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003240    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003250    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000325e    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  0000326c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003278    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003284    0000000c     systick.o (.text.get_systicks)
                  00003290    0000000c     Scheduler.o (.text.scheduler_init)
                  0000329c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000032a6    00000002     --HOLE-- [fill = 0]
                  000032a8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000032b0    00000006     libc.a : exit.c.obj (.text:abort)
                  000032b6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000032ba    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000032be    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000032c2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000032c6    00000002     --HOLE-- [fill = 0]

.cinit     0    00004d18    00000058     
                  00004d18    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004d47    00000001     --HOLE-- [fill = 0]
                  00004d48    0000000c     (__TI_handler_table)
                  00004d54    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004d5c    00000010     (__TI_cinit_table)
                  00004d6c    00000004     --HOLE-- [fill = 0]

.rodata    0    000032c8    00001a50     
                  000032c8    00000d5c     oled.o (.rodata.asc2_2412)
                  00004024    000005f0     oled.o (.rodata.asc2_1608)
                  00004614    00000474     oled.o (.rodata.asc2_1206)
                  00004a88    00000228     oled.o (.rodata.asc2_0806)
                  00004cb0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004cd8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004cec    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004cf6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004cf8    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004d00    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004d08    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004d0b    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004d0e    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004d11    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004d13    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    000001ce     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000010     empty.o (.data.Anolog)
                  202006f0    00000010     empty.o (.data.black)
                  20200700    00000010     empty.o (.data.white)
                  20200710    00000008     systick.o (.data.systicks)
                  20200718    00000004     empty.o (.data.D_Num)
                  2020071c    00000004     empty.o (.data.Run)
                  20200720    00000004     systick.o (.data.delay_times)
                  20200724    00000004     key.o (.data.key1_debounce)
                  20200728    00000004     key.o (.data.key2_debounce)
                  2020072c    00000001     bsp_usart.o (.data.uart_rx_index)
                  2020072d    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          850     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3630    291       516    
                                                                 
    .\app\
       Ganway.o                         1490    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       motor.o                          392     0         0      
       encoder.o                        362     0         16     
       key.o                            290     0         8      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3960    0         25     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12786   7006      2347   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004d5c records: 2, size/record: 8, table size: 16
	.data: load addr=00004d18, load size=0000002f bytes, run addr=20200560, run size=000001ce bytes, compression=lzss
	.bss: load addr=00004d54, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004d48 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000032b7  ADC0_IRQHandler                      
000032b7  ADC1_IRQHandler                      
000032b7  AES_IRQHandler                       
202006e0  Anolog                               
000032ba  C$$EXIT                              
000032b7  CANFD0_IRQHandler                    
000032b7  DAC0_IRQHandler                      
00002671  DL_ADC12_setClockConfig              
0000329d  DL_Common_delayCycles                
0000210d  DL_I2C_fillControllerTXFIFO          
00002b57  DL_I2C_setClockConfig                
000016f5  DL_SYSCTL_configSYSPLL               
000025e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001171  DL_Timer_initFourCCPWMMode           
00001369  DL_Timer_initTimerMode               
00002d55  DL_Timer_setCaptCompUpdateMethod     
00002fb9  DL_Timer_setCaptureCompareOutCtl     
00003231  DL_Timer_setCaptureCompareValue      
00002d71  DL_Timer_setClockConfig              
000024c9  DL_UART_init                         
000031d9  DL_UART_setClockConfig               
000032b7  DMA_IRQHandler                       
20200718  D_Num                                
000032b7  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000032b7  GROUP0_IRQHandler                    
00000e39  GROUP1_IRQHandler                    
000017d1  Get_Analog_value                     
000027a9  Get_Anolog_Value                     
00003251  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000032bb  HOSTexit                             
000032b7  HardFault_Handler                    
000032b7  I2C0_IRQHandler                      
000032b7  I2C1_IRQHandler                      
00002351  Key                                  
0000239d  Key_1                                
00003165  Left_Control                         
00003179  Left_Little_Control                  
000032b7  NMI_Handler                          
00000b8d  No_MCU_Ganv_Sensor_Init              
00001e95  No_MCU_Ganv_Sensor_Init_Frist        
0000262d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000020ad  OLED_Clear                           
00002891  OLED_ColorTurn                       
00002511  OLED_DisplayTurn                     
00001a81  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001617  OLED_Init                            
0000295d  OLED_Pow                             
00001c29  OLED_Refresh                         
00000695  OLED_ShowChar                        
00001535  OLED_ShowNum                         
0000194b  OLED_ShowSignedNum                   
000019e5  OLED_ShowString                      
00001f09  OLED_WR_Byte                         
000032b7  PendSV_Handler                       
000032b7  RTC_IRQHandler                       
000032bf  Reset_Handler                        
0000318d  Right_Control                        
000031a1  Right_Little_Control                 
2020071c  Run                                  
000032b7  SPI0_IRQHandler                      
000032b7  SPI1_IRQHandler                      
000032b7  SVC_Handler                          
00002559  SYSCFG_DL_ADC12_0_init               
00000865  SYSCFG_DL_GPIO_init                  
0000216d  SYSCFG_DL_I2C_OLED_init              
00001b11  SYSCFG_DL_PWM_0_init                 
000025a1  SYSCFG_DL_SYSCTL_init                
0000326d  SYSCFG_DL_SYSTICK_init               
000028c5  SYSCFG_DL_TIMER_0_init               
000021c5  SYSCFG_DL_UART_0_init                
000028f9  SYSCFG_DL_init                       
00001b9d  SYSCFG_DL_initPower                  
00000f59  Set_PWM                              
0000298d  SysTick_Handler                      
000032b7  TIMA0_IRQHandler                     
000032b7  TIMA1_IRQHandler                     
00001275  TIMG0_IRQHandler                     
000032b7  TIMG12_IRQHandler                    
000032b7  TIMG6_IRQHandler                     
000032b7  TIMG7_IRQHandler                     
000032b7  TIMG8_IRQHandler                     
000031eb  TI_memcpy_small                      
0000325f  TI_memset_small                      
000026b1  UART0_IRQHandler                     
000032b7  UART1_IRQHandler                     
000032b7  UART2_IRQHandler                     
000032b7  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004d5c  __TI_CINIT_Base                      
00004d6c  __TI_CINIT_Limit                     
00004d6c  __TI_CINIT_Warm                      
00004d48  __TI_Handler_Table_Base              
00004d54  __TI_Handler_Table_Limit             
000027e5  __TI_auto_init_nobinit_nopinit       
00001d31  __TI_decompress_lzss                 
000031fd  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003241  __TI_zero_init                       
00000a03  __adddf3                             
00002435  __aeabi_d2iz                         
00000a03  __aeabi_dadd                         
00002049  __aeabi_dcmpeq                       
00002085  __aeabi_dcmpge                       
00002099  __aeabi_dcmpgt                       
00002071  __aeabi_dcmple                       
0000205d  __aeabi_dcmplt                       
00001065  __aeabi_ddiv                         
00001451  __aeabi_dmul                         
000009f9  __aeabi_dsub                         
00002a15  __aeabi_i2d                          
00000693  __aeabi_idiv0                        
00003279  __aeabi_memclr                       
00003279  __aeabi_memclr4                      
00003279  __aeabi_memclr8                      
000032a9  __aeabi_memcpy                       
000032a9  __aeabi_memcpy4                      
000032a9  __aeabi_memcpy8                      
00002b7d  __aeabi_ui2d                         
000026f1  __aeabi_uidiv                        
000026f1  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001fe1  __cmpdf2                             
00001065  __divdf3                             
00001fe1  __eqdf2                              
00002435  __fixdfsi                            
00002a15  __floatsidf                          
00002b7d  __floatunsidf                        
00001dad  __gedf2                              
00001dad  __gtdf2                              
00001fe1  __ledf2                              
00001fe1  __ltdf2                              
UNDEFED   __mpu_init                           
00001451  __muldf3                             
00002821  __muldsi3                            
00001fe1  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009f9  __subdf3                             
00002b09  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000032c3  _system_pre_init                     
000032b1  abort                                
0000247f  adc_getValue                         
00004a88  asc2_0806                            
00004614  asc2_1206                            
00004024  asc2_1608                            
000032c8  asc2_2412                            
ffffffff  binit                                
202006f0  black                                
00001f75  convertAnalogToDigital               
00002be1  delay_ms                             
20200720  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003285  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
00001e21  key_debounce_process                 
00000d15  main                                 
000018a1  normalizeAnalogValues                
20200560  rx_buff                              
00003291  scheduler_init                       
2020055c  task_num                             
20200660  uart_rx_buffer                       
2020072c  uart_rx_index                        
2020072d  uart_rx_ticks                        
20200700  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000693  __aeabi_idiv0                        
00000695  OLED_ShowChar                        
00000865  SYSCFG_DL_GPIO_init                  
000009f9  __aeabi_dsub                         
000009f9  __subdf3                             
00000a03  __adddf3                             
00000a03  __aeabi_dadd                         
00000b8d  No_MCU_Ganv_Sensor_Init              
00000d15  main                                 
00000e39  GROUP1_IRQHandler                    
00000f59  Set_PWM                              
00001065  __aeabi_ddiv                         
00001065  __divdf3                             
00001171  DL_Timer_initFourCCPWMMode           
00001275  TIMG0_IRQHandler                     
00001369  DL_Timer_initTimerMode               
00001451  __aeabi_dmul                         
00001451  __muldf3                             
00001535  OLED_ShowNum                         
00001617  OLED_Init                            
000016f5  DL_SYSCTL_configSYSPLL               
000017d1  Get_Analog_value                     
000018a1  normalizeAnalogValues                
0000194b  OLED_ShowSignedNum                   
000019e5  OLED_ShowString                      
00001a81  OLED_DrawPoint                       
00001b11  SYSCFG_DL_PWM_0_init                 
00001b9d  SYSCFG_DL_initPower                  
00001c29  OLED_Refresh                         
00001d31  __TI_decompress_lzss                 
00001dad  __gedf2                              
00001dad  __gtdf2                              
00001e21  key_debounce_process                 
00001e95  No_MCU_Ganv_Sensor_Init_Frist        
00001f09  OLED_WR_Byte                         
00001f75  convertAnalogToDigital               
00001fe1  __cmpdf2                             
00001fe1  __eqdf2                              
00001fe1  __ledf2                              
00001fe1  __ltdf2                              
00001fe1  __nedf2                              
00002049  __aeabi_dcmpeq                       
0000205d  __aeabi_dcmplt                       
00002071  __aeabi_dcmple                       
00002085  __aeabi_dcmpge                       
00002099  __aeabi_dcmpgt                       
000020ad  OLED_Clear                           
0000210d  DL_I2C_fillControllerTXFIFO          
0000216d  SYSCFG_DL_I2C_OLED_init              
000021c5  SYSCFG_DL_UART_0_init                
00002351  Key                                  
0000239d  Key_1                                
00002435  __aeabi_d2iz                         
00002435  __fixdfsi                            
0000247f  adc_getValue                         
000024c9  DL_UART_init                         
00002511  OLED_DisplayTurn                     
00002559  SYSCFG_DL_ADC12_0_init               
000025a1  SYSCFG_DL_SYSCTL_init                
000025e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000262d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002671  DL_ADC12_setClockConfig              
000026b1  UART0_IRQHandler                     
000026f1  __aeabi_uidiv                        
000026f1  __aeabi_uidivmod                     
000027a9  Get_Anolog_Value                     
000027e5  __TI_auto_init_nobinit_nopinit       
00002821  __muldsi3                            
00002891  OLED_ColorTurn                       
000028c5  SYSCFG_DL_TIMER_0_init               
000028f9  SYSCFG_DL_init                       
0000295d  OLED_Pow                             
0000298d  SysTick_Handler                      
00002a15  __aeabi_i2d                          
00002a15  __floatsidf                          
00002b09  _c_int00_noargs                      
00002b57  DL_I2C_setClockConfig                
00002b7d  __aeabi_ui2d                         
00002b7d  __floatunsidf                        
00002be1  delay_ms                             
00002d55  DL_Timer_setCaptCompUpdateMethod     
00002d71  DL_Timer_setClockConfig              
00002fb9  DL_Timer_setCaptureCompareOutCtl     
00003165  Left_Control                         
00003179  Left_Little_Control                  
0000318d  Right_Control                        
000031a1  Right_Little_Control                 
000031d9  DL_UART_setClockConfig               
000031eb  TI_memcpy_small                      
000031fd  __TI_decompress_none                 
00003231  DL_Timer_setCaptureCompareValue      
00003241  __TI_zero_init                       
00003251  Get_Digtal_For_User                  
0000325f  TI_memset_small                      
0000326d  SYSCFG_DL_SYSTICK_init               
00003279  __aeabi_memclr                       
00003279  __aeabi_memclr4                      
00003279  __aeabi_memclr8                      
00003285  get_systicks                         
00003291  scheduler_init                       
0000329d  DL_Common_delayCycles                
000032a9  __aeabi_memcpy                       
000032a9  __aeabi_memcpy4                      
000032a9  __aeabi_memcpy8                      
000032b1  abort                                
000032b7  ADC0_IRQHandler                      
000032b7  ADC1_IRQHandler                      
000032b7  AES_IRQHandler                       
000032b7  CANFD0_IRQHandler                    
000032b7  DAC0_IRQHandler                      
000032b7  DMA_IRQHandler                       
000032b7  Default_Handler                      
000032b7  GROUP0_IRQHandler                    
000032b7  HardFault_Handler                    
000032b7  I2C0_IRQHandler                      
000032b7  I2C1_IRQHandler                      
000032b7  NMI_Handler                          
000032b7  PendSV_Handler                       
000032b7  RTC_IRQHandler                       
000032b7  SPI0_IRQHandler                      
000032b7  SPI1_IRQHandler                      
000032b7  SVC_Handler                          
000032b7  TIMA0_IRQHandler                     
000032b7  TIMA1_IRQHandler                     
000032b7  TIMG12_IRQHandler                    
000032b7  TIMG6_IRQHandler                     
000032b7  TIMG7_IRQHandler                     
000032b7  TIMG8_IRQHandler                     
000032b7  UART1_IRQHandler                     
000032b7  UART2_IRQHandler                     
000032b7  UART3_IRQHandler                     
000032ba  C$$EXIT                              
000032bb  HOSTexit                             
000032bf  Reset_Handler                        
000032c3  _system_pre_init                     
000032c8  asc2_2412                            
00004024  asc2_1608                            
00004614  asc2_1206                            
00004a88  asc2_0806                            
00004d48  __TI_Handler_Table_Base              
00004d54  __TI_Handler_Table_Limit             
00004d5c  __TI_CINIT_Base                      
00004d6c  __TI_CINIT_Limit                     
00004d6c  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
202006e0  Anolog                               
202006f0  black                                
20200700  white                                
20200718  D_Num                                
2020071c  Run                                  
20200720  delay_times                          
2020072c  uart_rx_index                        
2020072d  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[191 symbols]
