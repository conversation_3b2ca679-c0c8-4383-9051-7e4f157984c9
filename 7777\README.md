## Example Summary

Empty project using DriverLib.
This example shows a basic empty project using DriverLib with just main file
and SysConfig initialization.

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 按键消抖功能 (Key Debounce Feature)

本项目已集成按键消抖功能，提供稳定可靠的按键检测：

### 功能特点
- **硬件消抖**: 通过软件算法消除按键机械抖动
- **边沿检测**: 只在按键按下瞬间触发，避免重复触发
- **简洁高效**: 最小化代码实现，占用资源少
- **双按键支持**: 同时支持两个独立按键的消抖处理

### 技术参数
- **消抖时间**: 30ms (3 × 10ms定时器周期)
- **检测方式**: 上升沿触发
- **响应时间**: ≤40ms
- **内存占用**: 每按键仅需4字节

### 使用方法
```c
// 在10ms定时器中断中调用
Key();      // 处理按键1 (GPIOB.16)
Key_1();    // 处理按键2 (GPIOA.15)

// 检查按键状态
if(Flag_stop == 1) {
    // 按键1被按下
    Flag_stop = 0;  // 清除标志
}
if(Flag_stop1 == 1) {
    // 按键2被按下
    Flag_stop1 = 0; // 清除标志
}
```

### 配置说明
- 可通过修改 `DEBOUNCE_TIME` 宏调整消抖时间
- 按键配置为下拉输入，按下时为高电平
- 消抖算法自动处理按键释放，无需额外处理

## Example Usage

Compile, load and run the example.
