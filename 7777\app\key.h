#ifndef _KEY_H
#define _KEY_H
#include "ti_msp_dl_config.h"
#include "bsp_system.h"

#define KEY  DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)

// 按键消抖相关声明
typedef struct {
    uint8_t state;          // 当前稳定状态
    uint8_t last_read;      // 上次读取值
    uint8_t debounce_cnt;   // 消抖计数器
    uint8_t pressed;        // 按键按下标志
} key_debounce_t;

uint8_t key_debounce_process(key_debounce_t *key, uint8_t current_read); // 按键消抖处理
void Key(void);     // 按键1处理(带消抖)
void Key_1(void);   // 按键2处理(带消抖)

extern volatile int Flag_stop;  // 按键1标志
extern volatile int Flag_stop1; // 按键2标志

#endif
