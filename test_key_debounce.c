/*
 * 按键消抖功能测试程序
 * 测试目的：验证按键消抖算法的正确性
 */

#include <stdio.h>
#include <stdint.h>
#include <string.h>

// 模拟按键消抖结构体
typedef struct {
    uint8_t state;          // 当前稳定状态
    uint8_t last_read;      // 上次读取值
    uint8_t debounce_cnt;   // 消抖计数器
    uint8_t pressed;        // 按键按下标志
} key_debounce_t;

#define DEBOUNCE_TIME 3  // 消抖时间(10ms中断调用次数)

// 按键消抖处理函数
uint8_t key_debounce_process(key_debounce_t *key, uint8_t current_read)
{
    if (current_read != key->last_read) {
        key->debounce_cnt = 0;  // 重置计数器
        key->last_read = current_read;
        return 0;  // 状态不稳定
    }
    
    if (key->debounce_cnt < DEBOUNCE_TIME) {
        key->debounce_cnt++;
        return 0;  // 还在消抖期间
    }
    
    // 检测按键按下边沿
    if (current_read && !key->state) {
        key->pressed = 1;  // 按键按下
    }
    
    key->state = current_read;
    return key->pressed;
}

// 测试用例
void test_key_debounce()
{
    key_debounce_t test_key = {0};
    uint8_t test_sequence[] = {
        // 模拟按键抖动：0,1,0,1,1,1,1,0,1,0,0,0
        0,1,0,1,1,1,1,0,1,0,0,0,0,0,0,0
    };
    int sequence_len = sizeof(test_sequence);
    
    printf("按键消抖测试开始...\n");
    printf("时间(10ms) | 输入 | 输出 | 状态 | 计数器 | 说明\n");
    printf("---------|------|------|------|--------|--------\n");
    
    for(int i = 0; i < sequence_len; i++) {
        uint8_t result = key_debounce_process(&test_key, test_sequence[i]);
        
        printf("    %2d    |  %d   |  %d   |  %d   |   %d    | ", 
               i, test_sequence[i], result, test_key.state, test_key.debounce_cnt);
        
        if(result) {
            printf("按键触发!");
            test_key.pressed = 0; // 清除标志
        } else if(test_sequence[i] != test_key.last_read) {
            printf("检测到变化");
        } else if(test_key.debounce_cnt < DEBOUNCE_TIME) {
            printf("消抖中...");
        } else {
            printf("稳定状态");
        }
        printf("\n");
    }
    
    printf("\n测试完成！\n");
    printf("预期结果：只在第6个时间点(索引5)触发一次按键事件\n");
}

int main()
{
    test_key_debounce();
    return 0;
}
