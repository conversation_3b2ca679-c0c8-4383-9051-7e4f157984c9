/*
 * 按键消抖功能演示代码
 * 展示如何在实际项目中使用按键消抖功能
 */

#include "key.h"

// 模拟主循环中的按键处理
void key_demo_main_loop(void)
{
    // 检查按键1状态
    if(Flag_stop == 1) {
        // 按键1被按下，执行相应操作
        // 例如：切换LED状态、启动/停止电机等
        
        // 重要：处理完后清除标志
        Flag_stop = 0;
        
        // 这里可以添加你的业务逻辑
        // 例如：
        // DL_GPIO_togglePins(LED_PORT, LED_PIN_22_PIN);
        // 或者：Run = !Run;
    }
    
    // 检查按键2状态  
    if(Flag_stop1 == 1) {
        // 按键2被按下，执行相应操作
        
        // 重要：处理完后清除标志
        Flag_stop1 = 0;
        
        // 这里可以添加你的业务逻辑
        // 例如：
        // D_Num = (D_Num + 1) % 6;
        // OLED_ShowSignedNum(12, 16, D_Num, 1, 12, 1);
    }
}

/*
 * 按键消抖功能使用说明：
 * 
 * 1. 在10ms定时器中断中调用：
 *    Key();      // 处理按键1消抖
 *    Key_1();    // 处理按键2消抖
 * 
 * 2. 在主循环中检查按键状态：
 *    if(Flag_stop == 1) {
 *        // 按键1被按下
 *        Flag_stop = 0;  // 清除标志
 *        // 执行相应操作
 *    }
 * 
 * 3. 消抖参数调整：
 *    修改 DEBOUNCE_TIME 宏可以调整消抖时间
 *    当前设置：3 × 10ms = 30ms
 * 
 * 4. 特点：
 *    - 只在按键按下瞬间触发一次
 *    - 自动处理机械抖动
 *    - 内存占用极小（每按键4字节）
 *    - CPU占用极低
 */
